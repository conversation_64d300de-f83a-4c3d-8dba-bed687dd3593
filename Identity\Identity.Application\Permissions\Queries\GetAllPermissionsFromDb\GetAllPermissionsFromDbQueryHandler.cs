using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Identity.Domain.Entities;
using Identity.Domain.Repositories;
using MediatR;
using Microsoft.Extensions.Logging;
using Identity.Application.Common.Interfaces;

namespace Identity.Application.Permissions.Queries.GetAllPermissionsFromDb
{
    public class GetAllPermissionsFromDbQueryHandler : IRequestHandler<GetAllPermissionsFromDbQuery, GetAllPermissionsFromDbResponse>
    {
        private readonly Identity.Domain.Repositories.IPermissionRepository _permissionRepository;
        private readonly ILogger<GetAllPermissionsFromDbQueryHandler> _logger;
        private readonly ISubscriptionIntegrationService _subscriptionIntegrationService;
        private readonly IMenuRepository _menuRepository;

        public GetAllPermissionsFromDbQueryHandler(
            Identity.Domain.Repositories.IPermissionRepository permissionRepository,
            ILogger<GetAllPermissionsFromDbQueryHandler> logger,
            ISubscriptionIntegrationService subscriptionIntegrationService,
            IMenuRepository menuRepository)
        {
            _permissionRepository = permissionRepository;
            _logger = logger;
            _subscriptionIntegrationService = subscriptionIntegrationService;
            _menuRepository = menuRepository;
        }

        public async Task<GetAllPermissionsFromDbResponse> Handle(GetAllPermissionsFromDbQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting all permissions from database with filters: Category={Category}, SearchTerm={SearchTerm}, IsActive={IsActive}, UserType={UserType}, PageNumber={PageNumber}, PageSize={PageSize}",
                    request.Category, request.SearchTerm, request.IsActive, request.UserType, request.PageNumber, request.PageSize);

                // Get all permissions from database
                var allPermissions = await _permissionRepository.GetAllAsync();

                // Apply filters
                var filteredPermissions = allPermissions.AsQueryable();

                if (!string.IsNullOrEmpty(request.Category))
                {
                    filteredPermissions = filteredPermissions.Where(p =>
                        p.Category.Equals(request.Category, StringComparison.OrdinalIgnoreCase));
                }

                if (!string.IsNullOrEmpty(request.SearchTerm))
                {
                    filteredPermissions = filteredPermissions.Where(p =>
                        p.PermissionName.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                        p.Name.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                        p.Description.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                        p.Action.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase));
                }

                if (request.IsActive.HasValue)
                {
                    filteredPermissions = filteredPermissions.Where(p => p.IsActive == request.IsActive.Value);
                }

                var totalCount = filteredPermissions.Count();

                // Apply pagination
                var orderedPermissions = filteredPermissions
                    .OrderBy(p => p.PermissionName)
                    .ThenBy(p => p.Action);

                IQueryable<Permission> permissions = orderedPermissions;
                if (request.PageSize > 0)
                {
                    permissions = orderedPermissions
                        .Skip((request.PageNumber - 1) * request.PageSize)
                        .Take(request.PageSize);
                }

                var permissionDtos = permissions.Select(permission => new PermissionDto
                {
                    Id = permission.Id,
                    FeatureId = permission.FeatureId,
                    PermissionName = permission.PermissionName,
                    Action = permission.Action,
                    Description = permission.Description,
                    Name = permission.Name,
                    Category = permission.Category,
                    Resource = permission.Resource,
                    IsActive = permission.IsActive,
                    CreatedAt = permission.CreatedAt,
                    UpdatedAt = permission.UpdatedAt
                }).ToList();

                // Enrich with featureName, menuId, menuName
                var featureIds = permissionDtos.Where(p => p.FeatureId.HasValue).Select(p => p.FeatureId.Value).Distinct().ToList();
                _logger.LogDebug($"FeatureIds sent to subscription service: {string.Join(",", featureIds)}");

                var featureDetailsDict = new Dictionary<Guid, SubscriptionFeatureDetails>();
                try
                {
                    featureDetailsDict = await _subscriptionIntegrationService.GetFeatureFlagDetailsByIdsAsync(featureIds, request.UserType);
                    _logger.LogDebug($"FeatureFlag details returned: {System.Text.Json.JsonSerializer.Serialize(featureDetailsDict)}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to get feature details from subscription service. Continuing without feature enrichment.");
                }

                var missingFeatureIds = featureIds.Where(fid => !featureDetailsDict.ContainsKey(fid)).ToList();
                if (missingFeatureIds.Any())
                {
                    _logger.LogWarning($"FeatureIds not found in subscription service: {string.Join(",", missingFeatureIds)}");
                }

                var menuIds = featureDetailsDict.Values.Where(f => f.MenuId.HasValue).Select(f => f.MenuId.Value).Distinct().ToList();
                var menuDict = new Dictionary<Guid, string?>();
                foreach (var menuId in menuIds)
                {
                    try
                    {
                        var menu = await _menuRepository.GetByIdAsync(menuId);
                        menuDict[menuId] = menu?.MenuName;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to get menu name for MenuId: {MenuId}", menuId);
                        menuDict[menuId] = null;
                    }
                }

                foreach (var dto in permissionDtos)
                {
                    if (dto.FeatureId.HasValue && featureDetailsDict.TryGetValue(dto.FeatureId.Value, out var featureDetail))
                    {
                        dto.FeatureName = featureDetail.FeatureName;
                        dto.MenuId = featureDetail.MenuId;
                        if (featureDetail.MenuId.HasValue && menuDict.TryGetValue(featureDetail.MenuId.Value, out var menuName))
                        {
                            dto.MenuName = menuName;
                        }
                    }
                }

                // Calculate pagination info
                var totalPages = request.PageSize > 0 ? (int)Math.Ceiling((double)totalCount / request.PageSize) : 1;
                var hasNextPage = request.PageSize > 0 && request.PageNumber < totalPages;
                var hasPreviousPage = request.PageSize > 0 && request.PageNumber > 1;

                var response = new GetAllPermissionsFromDbResponse
                {
                    Permissions = permissionDtos,
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    TotalPages = totalPages,
                    HasNextPage = hasNextPage,
                    HasPreviousPage = hasPreviousPage
                };

                _logger.LogInformation("Successfully retrieved {Count} permissions out of {TotalCount} total permissions from database",
                    permissionDtos.Count, totalCount);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while getting all permissions from database");
                throw;
            }
        }
    }
}
