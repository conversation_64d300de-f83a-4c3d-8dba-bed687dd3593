{"ConnectionStrings": {"DefaultConnection": "Host=**************;Port=5432;Database=TLI_Identity;Username=postgres;Password=************", "Redis": "localhost:6379"}, "JwtSettings": {"Secret": "ThisIsAVerySecureKeyThatShouldBeStoredInASecureVault", "Issuer": "TriTrackzIdentity", "Audience": "TriTrackzServices", "AccessTokenExpirationMinutes": 2, "RefreshTokenExpirationDays": 7}, "ApiGateway": {"Url": "https://localhost:7000"}, "UseRabbitMQ": true, "RabbitMQ": {"Host": "localhost", "Port": 5672, "UserName": "guest", "Password": "guest"}, "PasswordChangeAttempts": {"MaxFailedAttempts": 3, "BlockDurationHours": 1}, "MobileChangeAttempts": {"MaxFailedAttempts": 3, "BlockDurationHours": 1}, "AdminRegistration": {"SecretKey": "TLI_ADMIN_SECRET_2025!"}, "MailConfig": {"FromAddress": "<EMAIL>", "FromName": "<PERSON><PERSON><PERSON>", "SendGridApiKey": "vzVGQnHd5MwhAjkm", "Host": "smtp-relay.brevo.com", "Username": "<EMAIL>", "Password": "vzVGQnHd5MwhAjkm", "Port": 587, "IsEmailEnabled": true, "ProxyEmail": "<EMAIL>", "ContactEmail": "<EMAIL>"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "AllowedOrigins": ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"], "Services": {"SubscriptionManagement": {"BaseUrl": "http://**************:5003"}}}