using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using SubscriptionManagement.Domain.Entities;
using SubscriptionManagement.Domain.Enums;
using SubscriptionManagement.Domain.Interfaces;
using SubscriptionManagement.Domain.Services;
using SubscriptionManagement.Infrastructure.Services;
using System.Text.Json;

namespace SubscriptionManagement.Infrastructure.Services;

public class FeatureFlagService : IFeatureFlagService
{
    private readonly IFeatureFlagRepository _featureFlagRepository;
    private readonly IMemoryCache _cache;
    private readonly ISubscriptionCacheService _cacheService;
    private readonly ILogger<FeatureFlagService> _logger;
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(5);

    public FeatureFlagService(
        IFeatureFlagRepository featureFlagRepository,
        IMemoryCache cache,
        ISubscriptionCacheService cacheService,
        ILogger<FeatureFlagService> logger)
    {
        _featureFlagRepository = featureFlagRepository;
        _cache = cache;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<bool> IsFeatureEnabledAsync(string featureKey, Guid userId, Dictionary<string, object>? context = null)
    {
        try
        {
            var featureFlag = await GetFeatureFlagByKeyAsync(featureKey);
            if (featureFlag == null)
            {
                _logger.LogWarning("Feature flag {FeatureKey} not found", featureKey);
                return false;
            }

            var isEnabled = featureFlag.IsActiveForUser(userId, context);

            if (isEnabled)
            {
                featureFlag.RecordUsage(userId, null, context);
                await _featureFlagRepository.UpdateAsync(featureFlag);
            }

            return isEnabled;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating feature flag {FeatureKey} for user {UserId}", featureKey, userId);
            return false;
        }
    }

    public async Task<T?> GetFeatureValueAsync<T>(string featureKey, Guid userId, T defaultValue, Dictionary<string, object>? context = null)
    {
        try
        {
            var featureFlag = await GetFeatureFlagByKeyAsync(featureKey);
            if (featureFlag == null || !featureFlag.IsActiveForUser(userId, context))
            {
                return defaultValue;
            }

            var variant = featureFlag.GetVariantForUser(userId, context);
            featureFlag.RecordUsage(userId, variant, context);
            await _featureFlagRepository.UpdateAsync(featureFlag);

            if (variant != null && TryConvertValue<T>(variant, out var convertedValue))
            {
                return convertedValue;
            }

            return defaultValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature value for {FeatureKey} and user {UserId}", featureKey, userId);
            return defaultValue;
        }
    }

    public async Task<string?> GetFeatureVariantAsync(string featureKey, Guid userId, Dictionary<string, object>? context = null)
    {
        try
        {
            var featureFlag = await GetFeatureFlagByKeyAsync(featureKey);
            if (featureFlag == null)
            {
                return null;
            }

            var variant = featureFlag.GetVariantForUser(userId, context);

            if (variant != null)
            {
                featureFlag.RecordUsage(userId, variant, context);
                await _featureFlagRepository.UpdateAsync(featureFlag);
            }

            return variant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting feature variant for {FeatureKey} and user {UserId}", featureKey, userId);
            return null;
        }
    }

    public async Task<string?> GetABTestVariantAsync(string testKey, Guid userId, Dictionary<string, object>? context = null)
    {
        try
        {
            var featureFlag = await GetFeatureFlagByKeyAsync(testKey);
            if (featureFlag == null || featureFlag.Type != FeatureFlagType.ABTest)
            {
                _logger.LogWarning("A/B test {TestKey} not found or not configured as A/B test", testKey);
                return null;
            }

            var variant = featureFlag.GetVariantForUser(userId, context);

            if (variant != null)
            {
                featureFlag.RecordUsage(userId, variant, context);
                await _featureFlagRepository.UpdateAsync(featureFlag);
            }

            return variant;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting A/B test variant for {TestKey} and user {UserId}", testKey, userId);
            return null;
        }
    }

    public async Task RecordConversionAsync(string featureKey, Guid userId, string? variant = null, Dictionary<string, object>? conversionData = null)
    {
        try
        {
            var featureFlag = await GetFeatureFlagByKeyAsync(featureKey);
            if (featureFlag == null)
            {
                return;
            }

            var context = conversionData ?? new Dictionary<string, object>();
            context["conversion"] = true;
            context["conversionTime"] = DateTime.UtcNow;

            featureFlag.RecordUsage(userId, variant, context);
            await _featureFlagRepository.UpdateAsync(featureFlag);

            _logger.LogInformation("Conversion recorded for feature {FeatureKey}, user {UserId}, variant {Variant}",
                featureKey, userId, variant);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error recording conversion for {FeatureKey} and user {UserId}", featureKey, userId);
        }
    }

    public async Task<FeatureFlag> CreateFeatureFlagAsync(string name, string description, string key, FeatureFlagType type, string? defaultValue = null)
    {
        var existingFlag = await _featureFlagRepository.GetByKeyAsync(key);
        if (existingFlag != null)
        {
            throw new InvalidOperationException($"Feature flag with key '{key}' already exists");
        }

        var featureFlag = new FeatureFlag(name, description, key, type, defaultValue);
        var result = await _featureFlagRepository.AddAsync(featureFlag);

        InvalidateCache(key);

        _logger.LogInformation("Created feature flag {FeatureKey} with type {Type}", key, type);
        return result;
    }

    public async Task<FeatureFlag?> GetFeatureFlagByIdAsync(Guid id)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByIdAsync(id);
            _logger.LogDebug("Retrieved feature flag {FeatureFlagId}", id);
            return featureFlag;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving feature flag {FeatureFlagId}", id);
            return null;
        }
    }

    public async Task<FeatureFlag> UpdateFeatureFlagAsync(Guid id, string? name = null, string? description = null, FeatureFlagStatus? status = null, Guid? menuId = null)
    {
        var featureFlag = await _featureFlagRepository.GetByIdAsync(id);
        if (featureFlag == null)
        {
            throw new ArgumentException($"Feature flag with ID {id} not found");
        }

        // Update properties if provided
        if (!string.IsNullOrEmpty(name))
        {
            featureFlag.UpdateName(name);
        }

        if (!string.IsNullOrEmpty(description))
        {
            featureFlag.UpdateDescription(description);
        }

        if (menuId.HasValue)
        {
            featureFlag.SetMenuId(menuId);
        }

        if (status.HasValue)
        {
            switch (status.Value)
            {
                case FeatureFlagStatus.Active:
                    featureFlag.Activate();
                    break;
                case FeatureFlagStatus.Inactive:
                    featureFlag.Deactivate();
                    break;
                case FeatureFlagStatus.Archived:
                    featureFlag.Archive();
                    break;
            }
        }

        await _featureFlagRepository.UpdateAsync(featureFlag);
        InvalidateCache(featureFlag.Key);

        return featureFlag;
    }

    public async Task ActivateFeatureFlagAsync(Guid id, DateTime? startDate = null, DateTime? endDate = null)
    {
        var featureFlag = await _featureFlagRepository.GetByIdAsync(id);
        if (featureFlag == null)
        {
            throw new ArgumentException($"Feature flag with ID {id} not found");
        }

        featureFlag.Activate(startDate, endDate);
        await _featureFlagRepository.UpdateAsync(featureFlag);
        InvalidateCache(featureFlag.Key);

        _logger.LogInformation("Activated feature flag {FeatureKey}", featureFlag.Key);
    }

    public async Task DeactivateFeatureFlagAsync(Guid id)
    {
        var featureFlag = await _featureFlagRepository.GetByIdAsync(id);
        if (featureFlag == null)
        {
            throw new ArgumentException($"Feature flag with ID {id} not found");
        }

        featureFlag.Deactivate();
        await _featureFlagRepository.UpdateAsync(featureFlag);
        InvalidateCache(featureFlag.Key);

        _logger.LogInformation("Deactivated feature flag {FeatureKey}", featureFlag.Key);
    }

    public async Task ArchiveFeatureFlagAsync(Guid id)
    {
        var featureFlag = await _featureFlagRepository.GetByIdAsync(id);
        if (featureFlag == null)
        {
            throw new ArgumentException($"Feature flag with ID {id} not found");
        }

        featureFlag.Archive();
        await _featureFlagRepository.UpdateAsync(featureFlag);
        InvalidateCache(featureFlag.Key);

        _logger.LogInformation("Archived feature flag {FeatureKey}", featureFlag.Key);
    }

    public async Task UpdateRolloutPercentageAsync(Guid id, int percentage)
    {
        var featureFlag = await _featureFlagRepository.GetByIdAsync(id);
        if (featureFlag == null)
        {
            throw new ArgumentException($"Feature flag with ID {id} not found");
        }

        featureFlag.UpdateRolloutPercentage(percentage);
        await _featureFlagRepository.UpdateAsync(featureFlag);
        InvalidateCache(featureFlag.Key);

        _logger.LogInformation("Updated rollout percentage for feature flag {FeatureKey} to {Percentage}%",
            featureFlag.Key, percentage);
    }

    public async Task<List<Guid>> GetUsersInRolloutAsync(Guid featureFlagId, int sampleSize = 100)
    {
        var usageHistory = await _featureFlagRepository.GetUsageHistoryAsync(featureFlagId, DateTime.UtcNow.AddDays(-30));
        return usageHistory
            .Select(u => u.UserId)
            .Distinct()
            .Take(sampleSize)
            .ToList();
    }

    private async Task<FeatureFlag?> GetFeatureFlagByKeyAsync(string key)
    {
        // Try Redis cache first
        var cachedFlag = await _cacheService.GetFeatureFlagAsync(key);
        if (cachedFlag != null)
        {
            return cachedFlag;
        }

        // Fallback to memory cache
        var memoryCacheKey = $"feature_flag_{key}";
        if (_cache.TryGetValue(memoryCacheKey, out FeatureFlag? memoryCachedFlag))
        {
            // Update Redis cache
            await _cacheService.SetFeatureFlagAsync(memoryCachedFlag, _cacheExpiration);
            return memoryCachedFlag;
        }

        // Load from database
        var featureFlag = await _featureFlagRepository.GetByKeyAsync(key);
        if (featureFlag != null)
        {
            // Cache in both Redis and memory
            await _cacheService.SetFeatureFlagAsync(featureFlag, _cacheExpiration);
            _cache.Set(memoryCacheKey, featureFlag, _cacheExpiration);
        }

        return featureFlag;
    }

    private async void InvalidateCache(string key)
    {
        // Remove from Redis cache
        await _cacheService.RemoveFeatureFlagAsync(key);

        // Remove from memory cache
        var memoryCacheKey = $"feature_flag_{key}";
        _cache.Remove(memoryCacheKey);
    }

    private bool TryConvertValue<T>(string value, out T? convertedValue)
    {
        try
        {
            if (typeof(T) == typeof(string))
            {
                convertedValue = (T)(object)value;
                return true;
            }

            if (typeof(T) == typeof(bool))
            {
                if (bool.TryParse(value, out var boolValue))
                {
                    convertedValue = (T)(object)boolValue;
                    return true;
                }
            }

            if (typeof(T) == typeof(int))
            {
                if (int.TryParse(value, out var intValue))
                {
                    convertedValue = (T)(object)intValue;
                    return true;
                }
            }

            if (typeof(T) == typeof(double))
            {
                if (double.TryParse(value, out var doubleValue))
                {
                    convertedValue = (T)(object)doubleValue;
                    return true;
                }
            }

            // Try JSON deserialization for complex types
            convertedValue = JsonSerializer.Deserialize<T>(value);
            return convertedValue != null;
        }
        catch
        {
            convertedValue = default;
            return false;
        }
    }

    // Additional methods implementation continues...
    public async Task AddRuleAsync(Guid featureFlagId, string name, string description, FeatureFlagRuleType type, string condition, int priority = 0)
    {
        var featureFlag = await _featureFlagRepository.GetByIdAsync(featureFlagId);
        if (featureFlag == null)
        {
            throw new ArgumentException($"Feature flag with ID {featureFlagId} not found");
        }

        var rule = new FeatureFlagRule(featureFlagId, name, description, type, condition, priority);
        featureFlag.AddRule(rule);

        await _featureFlagRepository.UpdateAsync(featureFlag);
        InvalidateCache(featureFlag.Key);
    }

    public async Task UpdateRuleAsync(Guid ruleId, string? condition = null, bool? isActive = null, int? priority = null)
    {
        // Implementation for updating rules
        throw new NotImplementedException("Rule update functionality to be implemented");
    }

    public async Task RemoveRuleAsync(Guid featureFlagId, Guid ruleId)
    {
        var featureFlag = await _featureFlagRepository.GetByIdAsync(featureFlagId);
        if (featureFlag == null)
        {
            throw new ArgumentException($"Feature flag with ID {featureFlagId} not found");
        }

        featureFlag.RemoveRule(ruleId);
        await _featureFlagRepository.UpdateAsync(featureFlag);
        InvalidateCache(featureFlag.Key);
    }

    public async Task<Dictionary<string, object>> GetFeatureFlagAnalyticsAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null)
    {
        var stats = await _featureFlagRepository.GetUsageStatsAsync(featureFlagId, from, to);
        var usageHistory = await _featureFlagRepository.GetUsageHistoryAsync(featureFlagId, from, to);

        return new Dictionary<string, object>
        {
            ["totalUsage"] = usageHistory.Count,
            ["uniqueUsers"] = usageHistory.Select(u => u.UserId).Distinct().Count(),
            ["variantDistribution"] = stats,
            ["conversionRate"] = CalculateConversionRate(usageHistory)
        };
    }

    public async Task<Dictionary<string, object>> GetABTestResultsAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null)
    {
        var usageHistory = await _featureFlagRepository.GetUsageHistoryAsync(featureFlagId, from, to);

        var variantStats = usageHistory
            .Where(u => !string.IsNullOrEmpty(u.Variant))
            .GroupBy(u => u.Variant)
            .ToDictionary(
                g => g.Key!,
                g => new
                {
                    Users = g.Select(u => u.UserId).Distinct().Count(),
                    Conversions = g.Count(u => u.Context.ContainsKey("conversion")),
                    ConversionRate = g.Count(u => u.Context.ContainsKey("conversion")) / (double)g.Select(u => u.UserId).Distinct().Count()
                }
            );

        return new Dictionary<string, object>
        {
            ["variants"] = variantStats,
            ["totalUsers"] = usageHistory.Select(u => u.UserId).Distinct().Count(),
            ["testDuration"] = (to ?? DateTime.UtcNow) - (from ?? usageHistory.Min(u => u.AccessedAt))
        };
    }

    public async Task<List<FeatureFlag>> GetFeatureFlagsForUserAsync(Guid userId, Dictionary<string, object>? context = null)
    {
        return await _featureFlagRepository.GetForUserAsync(userId, context);
    }

    public async Task<List<FeatureFlag>> GetAllFeatureFlagsAsync()
    {
        return await _featureFlagRepository.GetAllAsync();
    }

    public async Task<List<FeatureFlag>> GetActiveFeatureFlagsAsync()
    {
        return await _featureFlagRepository.GetActiveAsync();
    }

    public async Task<List<FeatureFlag>> GetFeatureFlagsByIdsAsync(List<Guid> ids, UserType? userType = null)
    {
        return await _featureFlagRepository.GetByIdsAsync(ids, userType);
    }

    public async Task BulkUpdateStatusAsync(List<Guid> featureFlagIds, FeatureFlagStatus status)
    {
        foreach (var id in featureFlagIds)
        {
            await UpdateFeatureFlagAsync(id, status: status);
        }
    }

    // Enhanced user targeting methods
    public async Task<bool> IsUserInTargetAudienceAsync(Guid featureFlagId, Guid userId, Dictionary<string, object>? context = null)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByIdAsync(featureFlagId);
            if (featureFlag == null)
            {
                return false;
            }

            return featureFlag.IsActiveForUser(userId, context);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if user {UserId} is in target audience for feature flag {FeatureFlagId}", userId, featureFlagId);
            return false;
        }
    }

    public async Task<List<Guid>> GetTargetedUsersAsync(Guid featureFlagId, int maxUsers = 1000)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByIdAsync(featureFlagId);
            if (featureFlag == null)
            {
                return new List<Guid>();
            }

            // For demonstration, return users from usage history
            // In a real implementation, you might query user service or subscription service
            var usageHistory = await _featureFlagRepository.GetUsageHistoryAsync(featureFlagId, DateTime.UtcNow.AddDays(-30));
            return usageHistory
                .Select(u => u.UserId)
                .Distinct()
                .Take(maxUsers)
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting targeted users for feature flag {FeatureFlagId}", featureFlagId);
            return new List<Guid>();
        }
    }

    // Enhanced gradual rollout methods
    public async Task<Dictionary<string, object>> GetRolloutStatusAsync(Guid featureFlagId)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByIdAsync(featureFlagId);
            if (featureFlag == null)
            {
                return new Dictionary<string, object>();
            }

            var usageHistory = await _featureFlagRepository.GetUsageHistoryAsync(featureFlagId, DateTime.UtcNow.AddDays(-7));
            var totalUsers = usageHistory.Select(u => u.UserId).Distinct().Count();
            var activeUsers = usageHistory.Where(u => u.AccessedAt >= DateTime.UtcNow.AddDays(-1))
                .Select(u => u.UserId).Distinct().Count();

            return new Dictionary<string, object>
            {
                ["featureFlagId"] = featureFlagId,
                ["rolloutPercentage"] = featureFlag.RolloutPercentage,
                ["status"] = featureFlag.Status.ToString(),
                ["totalUsersInPeriod"] = totalUsers,
                ["activeUsersToday"] = activeUsers,
                ["estimatedReach"] = totalUsers * (featureFlag.RolloutPercentage / 100.0),
                ["lastUpdated"] = featureFlag.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting rollout status for feature flag {FeatureFlagId}", featureFlagId);
            return new Dictionary<string, object>();
        }
    }

    public async Task<bool> CanIncreaseRolloutAsync(Guid featureFlagId, int targetPercentage)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByIdAsync(featureFlagId);
            if (featureFlag == null)
            {
                return false;
            }

            // Check if the feature flag is stable (no recent errors, good performance metrics)
            var recentUsage = await _featureFlagRepository.GetUsageHistoryAsync(featureFlagId, DateTime.UtcNow.AddHours(-24));
            var errorRate = CalculateErrorRate(recentUsage);

            // Only allow rollout increase if error rate is below 5%
            return errorRate < 0.05 && targetPercentage > featureFlag.RolloutPercentage && targetPercentage <= 100;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if rollout can be increased for feature flag {FeatureFlagId}", featureFlagId);
            return false;
        }
    }

    // Enhanced A/B testing methods
    public async Task<Dictionary<string, object>> GetABTestPerformanceAsync(Guid featureFlagId, DateTime? from = null, DateTime? to = null)
    {
        try
        {
            var featureFlag = await _featureFlagRepository.GetByIdAsync(featureFlagId);
            if (featureFlag == null || featureFlag.Type != FeatureFlagType.ABTest)
            {
                return new Dictionary<string, object>();
            }

            var fromDate = from ?? DateTime.UtcNow.AddDays(-30);
            var toDate = to ?? DateTime.UtcNow;

            var usageHistory = await _featureFlagRepository.GetUsageHistoryAsync(featureFlagId, fromDate, toDate);

            var variantPerformance = usageHistory
                .Where(u => !string.IsNullOrEmpty(u.Variant))
                .GroupBy(u => u.Variant!)
                .ToDictionary(g => g.Key, g => (object)new
                {
                    users = g.Select(u => u.UserId).Distinct().Count(),
                    conversions = g.Count(u => u.Context.ContainsKey("conversion")),
                    conversionRate = CalculateConversionRate(g.ToList()),
                    averageSessionDuration = g.Where(u => u.Context.ContainsKey("sessionDuration"))
                        .Average(u => Convert.ToDouble(u.Context.GetValueOrDefault("sessionDuration", 0)))
                });

            return new Dictionary<string, object>
            {
                ["featureFlagId"] = featureFlagId,
                ["testPeriod"] = new { from = fromDate, to = toDate },
                ["variants"] = variantPerformance,
                ["totalUsers"] = usageHistory.Select(u => u.UserId).Distinct().Count(),
                ["statisticalSignificance"] = CalculateStatisticalSignificance(variantPerformance)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting A/B test performance for feature flag {FeatureFlagId}", featureFlagId);
            return new Dictionary<string, object>();
        }
    }

    public async Task<string?> GetWinningVariantAsync(Guid featureFlagId)
    {
        try
        {
            var performance = await GetABTestPerformanceAsync(featureFlagId);
            if (!performance.ContainsKey("variants"))
            {
                return null;
            }

            var variants = performance["variants"] as Dictionary<string, object>;
            if (variants == null || !variants.Any())
            {
                return null;
            }

            // Simple winning variant selection based on conversion rate
            // In a real implementation, you'd use proper statistical analysis
            var bestVariant = variants
                .Where(v => v.Value != null)
                .OrderByDescending(v =>
                {
                    var variantData = v.Value as dynamic;
                    return variantData?.conversionRate ?? 0;
                })
                .FirstOrDefault();

            return bestVariant.Key;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting winning variant for feature flag {FeatureFlagId}", featureFlagId);
            return null;
        }
    }

    private double CalculateConversionRate(List<FeatureFlagUsage> usageHistory)
    {
        var totalUsers = usageHistory.Select(u => u.UserId).Distinct().Count();
        var conversions = usageHistory.Count(u => u.Context.ContainsKey("conversion"));

        return totalUsers > 0 ? conversions / (double)totalUsers : 0;
    }

    private double CalculateErrorRate(List<FeatureFlagUsage> usageHistory)
    {
        var totalRequests = usageHistory.Count;
        var errors = usageHistory.Count(u => u.Context.ContainsKey("error") || u.Context.ContainsKey("exception"));

        return totalRequests > 0 ? errors / (double)totalRequests : 0;
    }

    private double CalculateStatisticalSignificance(Dictionary<string, object> variantPerformance)
    {
        // Simplified statistical significance calculation
        // In a real implementation, you'd use proper statistical tests like Chi-square or Z-test
        if (variantPerformance.Count < 2)
        {
            return 0;
        }

        var totalUsers = variantPerformance.Values
            .Select(v =>
            {
                try
                {
                    var dynamicValue = v as dynamic;
                    return (int)(dynamicValue?.users ?? 0);
                }
                catch
                {
                    return 0;
                }
            })
            .Sum();

        // Return a mock significance value based on sample size
        return totalUsers > 1000 ? 0.95 : totalUsers > 100 ? 0.80 : 0.50;
    }
}
